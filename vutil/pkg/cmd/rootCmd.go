package cmd

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/rs/zerolog/log"
	"github.com/spf13/cobra"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/internal/handler"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/cmd/dbtun"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/cmd/port_forward"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/cmd/sqlarch"
)

var (
	// Flags
	Env string

	// Command definition
	rootCmd = &cobra.Command{
		Use:   "vutil",
		Short: "vutil is a tool utility designed to simplify everyday's task for Viewbid project",
		Long: `Intended to bring automation to developers life,
so they can focus on what matters for them, instead of dealing with operational complexity.`,
	}
)

func init() {
	rootCmd.CompletionOptions.DisableDefaultCmd = true
}

func Execute() {
	// Handle termination signals to gracefully shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGTERM, syscall.SIGINT)
	go func() {
		s := <-sigChan
		fmt.Println()
		log.Debug().Msgf("Received signal: %s - Stopping processes", s.String())
		handler.DoneChan <- struct{}{}
		handler.Done = true
	}()

	// Add the commands
	rootCmd.AddCommand(dbtun.DBTunCmd)         // Adding the database tunnel command
	rootCmd.AddCommand(sqlarch.SQLArchCmd)     // Adding the SQL Architecture tunnel command
	rootCmd.AddCommand(port_forward.PortFwCmd) // Adding the port forwarder command

	if err := rootCmd.Execute(); err != nil {
		log.Error().Msg(err.Error())
		os.Exit(1)
	}

	fmt.Println("\nThank you for using this tool, hope it was useful 🚀🚀!")
}
