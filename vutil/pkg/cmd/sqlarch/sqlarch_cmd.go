package sqlarch

import (
	"github.com/spf13/cobra"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/logging"
)

var (
	debug     bool
	env       string
	localPort string
)

var SQLArchCmd = &cobra.Command{
	Use:   "archdb",
	Short: "Use `archdb` to open a tunnel to the remote database to make change to the architectural model",
	Run: func(cmd *cobra.Command, args []string) {
		logging.SetupLogger(debug)

		cArgs := sqlArchCmdArgs{
			env:       env,
			localPort: localPort,
		}
		Exec(&cArgs)
	},
}

func init() {
	SQLArchCmd.Flags().BoolVar(&debug, "debug", false, "When set to 'true', will display useful information")
	SQLArchCmd.Flags().StringVarP(&env, "env", "e", "dev", "Environment in which to execute the action. Can either be 'dev', 'pp'")
	SQLArchCmd.Flags().StringVarP(&localPort, "port", "p", "3306", "Local port to listen on, that will forward to the remote database")
}
