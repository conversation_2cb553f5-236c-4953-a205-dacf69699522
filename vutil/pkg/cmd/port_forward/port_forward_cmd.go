package port_forward

import (
	"github.com/spf13/cobra"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/logging"
)

var (
	remote        string
	sshPort       string
	localPort     string
	username      string
	password      string
	sshKeyPath    string
	sshKeyContent string
	debug         bool
	env           string
)

var PortFwCmd = &cobra.Command{
	Use:   "pfw",
	Short: "Use `pfw` to forward a local connection to a remote target from the SSH connection",
	Run: func(cmd *cobra.Command, args []string) {
		logging.SetupLogger(debug)

		cArgs := portForwardCmdArgs{
			remote:        remote,
			localPort:     localPort,
			sshPort:       sshPort,
			sshUsername:   username,
			sshPassword:   password,
			sshKeyPath:    sshKeyPath,
			sshKeyContent: sshKeyContent,
			env:           env,
		}
		Exec(&cArgs)
	},
}

func init() {
	// Flags
	PortFwCmd.Flags().StringVarP(&remote, "remote", "r", "", "Remote target address to connect to. Must be in [address:port] format")
	PortFwCmd.Flags().StringVarP(&localPort, "port", "p", "9097", "Local port to listen on that will forward to the remote database")
	PortFwCmd.Flags().StringVarP(&sshPort, "ssh-port", "s", "22", "Port on which SSH is listening")
	PortFwCmd.Flags().StringVarP(&env, "env", "e", "dev", "Environment in which to execute the action. Can either be 'dev' or 'pp'")

	PortFwCmd.Flags().StringVar(&username, "ssh-username", "", "SSH username")
	PortFwCmd.Flags().StringVar(&password, "ssh-password", "", "SSH password")
	PortFwCmd.Flags().StringVar(&sshKeyPath, "sshKeyPath", "", "Path to the private key")
	PortFwCmd.Flags().StringVar(&sshKeyContent, "sshKeyContent", "", "Content of the private key")

	// Required flags
	PortFwCmd.MarkFlagsOneRequired("sshKeyPath", "ssh-password", "sshKeyContent", "env") // Either use SSH private key or password and errors out if developer doesn't pass one of them
	PortFwCmd.MarkFlagRequired("remote")
}
