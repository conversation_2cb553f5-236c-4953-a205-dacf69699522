package port_forward

import (
	"github.com/rs/zerolog/log"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/internal/handler"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/aws"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/tunnel"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/ui"
)

type portForwardCmdArgs struct {
	localPort     string
	remote        string
	sshPort       string
	sshUsername   string
	sshPassword   string
	sshKeyPath    string
	sshKeyContent string
	env           string
}

func Exec(args *portForwardCmdArgs) {
	// Create AWS client
	awsClient, err := aws.NewClient(args.env)
	if err != nil {
		log.Fatal().Msgf("could not create AWS client: %s", err)
	}

	// Create port forwarder
	pfw, err := tunnel.CreatePortForwarder(awsClient, args.remote, args.localPort, &handler.<PERSON><PERSON>han)
	if err != nil {
		log.Fatal().Msgf("could not create PortForwarder: %s", err)
	}

	// Display connection message
	ui.RenderConnectionMessage(pfw)

	// Wait until user stops the program manually
	go func() {
		err = pfw.Forward()
		if err != nil {
			log.Fatal().Msgf("could not listen for connection: %s", err)
		}
	}()

	pfw.Wait()
}
