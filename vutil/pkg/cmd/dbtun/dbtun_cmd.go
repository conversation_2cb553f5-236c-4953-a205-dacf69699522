package dbtun

import (
	"github.com/spf13/cobra"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/logging"
)

var (
	debug     bool
	env       string
	localPort string
)

var DBTunCmd = &cobra.Command{
	Use:   "dbtun",
	Short: "Use `dbtun` to open a tunnel to the remote database in dev or qa environments",
	Run: func(cmd *cobra.Command, args []string) {
		logging.SetupLogger(debug)

		cArgs := dbtunCmdArgs{
			env:       env,
			localPort: localPort,
		}
		Exec(&cArgs)
	},
}

func init() {
	DBTunCmd.Flags().BoolVar(&debug, "debug", false, "When set to 'true', will display useful information")
	DBTunCmd.Flags().StringVarP(&env, "env", "e", "dev", "Environment in which to execute the action. Can either be 'dev', 'qa' or 'pp'")
	DBTunCmd.Flags().StringVarP(&localPort, "port", "p", "5432", "Local port to listen on that will forward to the remote database")
}
