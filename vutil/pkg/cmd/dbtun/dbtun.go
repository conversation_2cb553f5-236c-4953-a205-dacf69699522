package dbtun

import (
	"github.com/rs/zerolog/log"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/internal/forwarder"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/internal/handler"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/aws"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/config"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/tunnel"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/ui"
)

type dbtunCmdArgs struct {
	env       string
	localPort string
}

// Exec executes the database tunnel command
func Exec(args *dbtunCmdArgs) {
	// Check if aws-cli is installed
	err := aws.PreflightChecks()
	if err != nil {
		log.Fatal().AnErr("error", err).Msg("preflight checks did not pass, please check the reported error")
	}

	// Create AWS client
	awsClient, err := aws.NewClient(args.env)
	if err != nil {
		log.Fatal().Msgf("could not create AWS client: %s", err)
	}

	// Create port forwarder
	pfw, err := tunnel.CreatePortForwarder(awsClient, config.GetDatabaseAddress(args.env), args.localPort, &handler.DoneChan)
	if err != nil {
		log.Fatal().Msgf("could not create port forwarder: %s", err)
	}

	// forward requests
	go func() {
		err = pfw.Forward()
		if err != nil {
			log.Fatal().Msgf("could not listen for connection on SSH port forwarder: %s", err)
		}
	}()

	// Render connection info & table
	renderTable(pfw, args.env, awsClient)

	// Wait for connection
	pfw.Wait()
}

// renderTable displays database connection information
func renderTable(pfw *forwarder.PortForwarder, env string, awsClient *aws.Client) {
	pwd, err := awsClient.GetDatabasePassword(config.PostgresPasswordParameter)
	if err != nil {
		log.Error().AnErr("error", err).Msg("could not retrieve database password")
		pwd = "ERROR_RETRIEVING_PASSWORD"
	}
	encodedPwd := aws.EncodePassword(pwd)

	info := ui.DatabaseConnectionInfo{
		Environment:     env,
		Username:        "auro_user",
		Password:        pwd,
		EncodedPassword: encodedPwd,
		Port:            pfw.Port(),
	}

	ui.RenderDatabaseTable(info)
}
