package logging

import (
	"os"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// SetupLogger configures the global logger with the specified debug level
func SetupLogger(debug bool) {
	ll := zerolog.InfoLevel
	if debug {
		ll = zerolog.DebugLevel
	}

	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	cw := zerolog.ConsoleWriter{Out: os.Stdout, TimeFormat: time.RFC822}
	log.Logger = zerolog.New(cw).Level(ll).With().Timestamp().Logger()
}
