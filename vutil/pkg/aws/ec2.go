package aws

import (
	"context"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
)

// GetActiveInstance retrieves the first running EC2 instance and connect endpoint
func (c *Client) GetActiveInstance() (*types.Instance, string, error) {
	ec2Client := ec2.NewFromConfig(*c.config)
	dio, err := ec2Client.DescribeInstances(context.TODO(), &ec2.DescribeInstancesInput{})
	if err != nil {
		return nil, "", err
	}

	instances := extractInstances(dio)
	if len(instances) == 0 {
		return nil, "", fmt.Errorf("no active instance running currently")
	}

	endpoints, err := ec2Client.DescribeInstanceConnectEndpoints(context.TODO(), &ec2.DescribeInstanceConnectEndpointsInput{})
	if err != nil {
		return nil, "", err
	}

	if len(endpoints.InstanceConnectEndpoints) == 0 {
		return nil, "", fmt.Errorf("no instance connect endpoints found")
	}

	for _, v := range instances {
		if v.State.Name == types.InstanceStateNameRunning {
			return &v, *endpoints.InstanceConnectEndpoints[0].DnsName, nil
		}
	}

	return nil, "", fmt.Errorf("could not retrieve an active instance")
}

// extractInstances extracts instances from DescribeInstancesOutput
func extractInstances(dio *ec2.DescribeInstancesOutput) map[string]types.Instance {
	instances := make(map[string]types.Instance)

	for _, v := range dio.Reservations {
		for _, instance := range v.Instances {
			instances[*instance.InstanceId] = instance
		}
	}

	return instances
}
