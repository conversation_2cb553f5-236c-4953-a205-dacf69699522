package aws

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/rs/zerolog/log"
	conf "gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/config"
)

const (
	LoginMaxRetries = 3
)

// Client wraps AWS operations
type Client struct {
	config *aws.Config
}

// NewClient creates a new AWS client with authentication
func NewClient(env string) (*Client, error) {
	cfg, err := login(env)
	if err != nil {
		return nil, fmt.Errorf("could not authenticate to AWS: %w", err)
	}

	return &Client{config: cfg}, nil
}

// Config returns the underlying AWS config
func (c *Client) Config() *aws.Config {
	return c.config
}

// login handles AWS SSO authentication
func login(env string) (*aws.Config, error) {
	ctx := context.TODO()
	homeDir, _ := os.UserHomeDir()
	cf := path.Join(homeDir, ".vutil", "config")

	// Generate config file
	err := generateConfigFile(cf)
	if err != nil {
		return nil, err
	}

	// Load AWS profile
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithSharedConfigFiles([]string{cf}),
		config.WithSharedConfigProfile(fmt.Sprintf("debugger-%s", env)))
	if err != nil {
		return nil, err
	}

	// Try to authenticate
	log.Debug().Msg("Authenticating to AWS...")
	log.Info().Msg("Attempting to retrieve AWS credentials...")
	for i := 0; i < LoginMaxRetries; i++ {
		_, err := cfg.Credentials.Retrieve(ctx)
		if err == nil {
			if i == 0 {
				log.Info().Msg("Reusing cached AWS credentials...")
			}
			break
		}

		log.Debug().Int("attempt", i+1).Msg("Could not retrieve the credentials")
		err = ssoLogin(cf, env)
		if err != nil && i >= LoginMaxRetries-1 {
			log.Debug().Msg("Max retries reached")
			return nil, err
		}
	}

	return &cfg, nil
}

// generateConfigFile creates the AWS config file
func generateConfigFile(pf string) error {
	// Create folders if they don't exist
	err := os.MkdirAll(path.Dir(pf), os.ModePerm)
	if err != nil {
		return err
	}

	// Create config file
	f, err := os.Create(pf)
	if err != nil {
		return err
	}
	defer f.Close()

	n, err := f.WriteString(conf.AWSConfig)
	if err != nil {
		return err
	}
	log.Debug().Str("file_path", pf).Int("bytes_written", n).Msg("Created configuration file successfully")

	return nil
}

// ssoLogin performs AWS SSO login
func ssoLogin(confPath, env string) error {
	ssoLoginCmd := exec.Command("aws", "sso", "login", "--sso-session", "debugger", "--profile", fmt.Sprintf("debugger-%s", env))
	ssoLoginCmd.Env = append(ssoLoginCmd.Env, os.Environ()...)
	ssoLoginCmd.Env = append(ssoLoginCmd.Env, fmt.Sprintf("AWS_CONFIG_FILE=%s", confPath))
	ssoLoginCmd.Stdout = os.Stdout
	ssoLoginCmd.Stderr = os.Stderr

	// Start the command and wait until completion
	log.Debug().
		Str("cmd_path", ssoLoginCmd.Path).
		Strs("env_variables", ssoLoginCmd.Env).
		Strs("cmd_args", ssoLoginCmd.Args).
		Msgf("Running command: %s", ssoLoginCmd.String())

	err := ssoLoginCmd.Start()
	if err != nil {
		log.Error().AnErr("error", err).Msg("Could not start the 'aws' command, check the reported error")
		return err
	}

	err = ssoLoginCmd.Wait()
	if err != nil {
		fmt.Println()
		log.Error().Str("error", err.Error()).Msg("Got an error when login to AWS sso")
	}

	fmt.Println()
	return err
}

// PreflightChecks verifies AWS CLI is installed
func PreflightChecks() error {
	path, err := exec.LookPath("aws")
	if err != nil {
		return err
	}

	log.Debug().Str("aws_binary_path", path).Msg("aws cli is installed")
	return nil
}
