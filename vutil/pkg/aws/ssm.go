package aws

import (
	"context"
	"net/url"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/ssm"
)

// GetSSH<PERSON>ey retrieves SSH private key from SSM Parameter Store
func (c *Client) GetSSHKey() (string, error) {
	ssmClient := ssm.NewFromConfig(*c.config)
	paramOutput, err := ssmClient.GetParameter(context.TODO(), &ssm.GetParameterInput{
		Name:           aws.String("vb-dev-prime-ssh-key"),
		WithDecryption: aws.Bool(true),
	})
	if err != nil {
		return "", err
	}

	return *paramOutput.Parameter.Value, nil
}

// GetDatabasePassword retrieves database password from SSM Parameter Store
func (c *Client) GetDatabasePassword(parameterName string) (string, error) {
	ssmClient := ssm.NewFromConfig(*c.config)
	paramOutput, err := ssmClient.GetParameter(context.TODO(), &ssm.GetParameterInput{
		Name:           aws.String(parameterName),
		WithDecryption: aws.Bool(true),
	})
	if err != nil {
		return "", err
	}

	return *paramOutput.Parameter.Value, nil
}

// EncodePassword URL-encodes a password for use in connection strings
func EncodePassword(pwd string) string {
	return url.QueryEscape(pwd)
}
