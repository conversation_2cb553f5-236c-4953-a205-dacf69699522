package crypto

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"

	"golang.org/x/crypto/ssh"
)

// ParsePrivateKey parses a PEM-encoded RSA private key
func ParsePrivateKey(key string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(key))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}
	return x509.ParsePKCS1PrivateKey(block.Bytes)
}

// GetPrivateKeyFromFile reads and parses a private key from file
func GetPrivateKeyFromFile(path string) (*rsa.PrivateKey, error) {
	b, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(b)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block from file")
	}
	return x509.ParsePKCS1PrivateKey(block.Bytes)
}

// GetSSHSigner creates an SSH signer from a private key file
func GetSSHSigner(path string) (ssh.Signer, error) {
	pk, err := GetPrivateKeyFromFile(path)
	if err != nil {
		return nil, err
	}
	return ssh.NewSignerFromKey(pk)
}
