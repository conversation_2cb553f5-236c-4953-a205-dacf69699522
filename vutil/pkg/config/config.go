package config

// AWSConfig contains the AWS configuration template
const AWSConfig = `[profile debugger-dev]
sso_session = debugger
sso_account_id = ************
sso_role_name = ViewBidDefaultAccess
region = ca-central-1
output = json
[profile debugger-qa]
sso_session = debugger
sso_account_id = ************
sso_role_name = ViewBidDefaultAccess
region = ca-central-1
output = json
[profile debugger-pp]
sso_session = debugger
sso_account_id = ************
sso_role_name = ViewBidDefaultAccess
region = ca-central-1
output = json
[sso-session debugger]
sso_start_url = https://d-9d67714e4c.awsapps.com/start
sso_region = ca-central-1
sso_registration_scopes = sso:account:access`

// Database addresses for different environments
const (
	DevDBAddress = "vb-dev-prime.cluster-cs0eiu0wpif9.ca-central-1.rds.amazonaws.com:19001"
	QADBAddress  = "vb-qa-prime.cluster-ctmz6dk1fiy7.ca-central-1.rds.amazonaws.com:19001"
	PPDBAddress  = "vb-pp-prime.cluster-c7wk86k68hpe.ca-central-1.rds.amazonaws.com:19001"
	
	// SQL Architecture database
	DevArchDBAddress = "vb-arch-instance-1.cs0eiu0wpif9.ca-central-1.rds.amazonaws.com:3306"
)

// SSM Parameter names
const (
	SSHKeyParameter           = "vb-dev-prime-ssh-key"
	PostgresPasswordParameter = "vb-dev-prime-aurora-password"
	MySQLPasswordParameter    = "vb-dev-prime-mysql-password"
)

// GetDatabaseAddress returns the database address for the given environment
func GetDatabaseAddress(env string) string {
	switch env {
	case "qa":
		return QADBAddress
	case "pp":
		return PPDBAddress
	default:
		return DevDBAddress
	}
}

// GetArchDatabaseAddress returns the architecture database address
func GetArchDatabaseAddress() string {
	return DevArchDBAddress
}
