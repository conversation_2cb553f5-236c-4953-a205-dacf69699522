package ui

import (
	"fmt"
	"os"

	"github.com/jedib0t/go-pretty/table"
	"github.com/jedib0t/go-pretty/text"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/internal/forwarder"
)

// DatabaseConnectionInfo holds information for rendering database connection details
type DatabaseConnectionInfo struct {
	Environment     string
	Username        string
	Password        string
	EncodedPassword string
	Port            string
}

// RenderDatabaseTable renders a formatted table with database connection information
func RenderDatabaseTable(info DatabaseConnectionInfo) {
	t := table.NewWriter()
	t.SetOutputMirror(os.Stdout)
	t.Style().Options.DrawBorder = false
	t.Style().Options.SeparateHeader = false
	t.Style().Color.Header = text.Colors{text.BgHiCyan, text.FgBlack}
	t.Style().Color.Row = text.Colors{text.BgGreen, text.FgBlack}

	t.AppendHeader(table.Row{"Environment", "Username", "Password", "Encoded password (for prisma)"})
	t.AppendRows([]table.Row{
		{info.Environment, info.Username, info.Password, info.EncodedPassword},
	})

	fmt.Println()
	fmt.Printf("Your database forwarder is ready to accept connections on '%s:%s'...\n",
		text.Colors{text.FgHiCyan, text.Bold}.Sprint("127.0.0.1"),
		text.Colors{text.FgHiGreen, text.Bold}.Sprint(info.Port))
	fmt.Println()
	t.Render()
}

// RenderConnectionMessage displays a simple connection ready message with colored formatting
func RenderConnectionMessage(pfw *forwarder.PortForwarder) {
	fmt.Printf("Listening on '%s:%s'\n",
		text.Colors{text.FgHiCyan, text.Bold}.Sprint("127.0.0.1"),
		text.Colors{text.FgHiGreen, text.Bold}.Sprint(pfw.Port()))
}
