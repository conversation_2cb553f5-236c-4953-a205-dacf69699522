package tunnel

import (
	"fmt"

	"github.com/rs/zerolog/log"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/internal/forwarder"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/aws"
	"gitlab.com/binaryStarTech/product/viewbid-platform/viewbid-development/vutil/pkg/crypto"
)

// CreatePortForwarder creates a port forwarder using AWS EC2 Instance Connect
func CreatePortForwarder(awsClient *aws.Client, remoteConn string, localPort string, doneChan *chan struct{}) (*forwarder.PortForwarder, error) {
	// Retrieve first active instance & connect endpoint details
	log.Info().Msg("Retrieving active instance...")
	instance, connectEndpoint, err := awsClient.GetActiveInstance()
	if err != nil {
		return nil, fmt.Errorf("could not retrieve active instance: %w", err)
	}
	log.Debug().Str("instance_id", *instance.InstanceId).Msg("Found a running EC2 instance")

	// Create instance-connect websocket connection forwarder
	log.Debug().Msg("Starting Websocket port forwarder...")
	ec2fwOpts := forwarder.InstanceConnectForwarderOpts{
		ConnectEndpointDNS: connectEndpoint,
		Instance:           instance,
		RemotePort:         "22",
		AWSConfig:          awsClient.Config(),
	}
	ec2fw := forwarder.NewInstanceConnectForwarder(&ec2fwOpts)
	go ec2fw.Forward() // since blocking, needs to be run in a goroutine

	// Wait until instance connect forwarder is ready
	<-ec2fw.ReadyChan()

	// Retrieve & parse SSH private key
	sshKey, err := awsClient.GetSSHKey()
	if err != nil {
		return nil, fmt.Errorf("could not retrieve private key: %w", err)
	}
	pk, err := crypto.ParsePrivateKey(sshKey)
	if err != nil {
		return nil, fmt.Errorf("could not parse private key: %w", err)
	}

	// Create SSH port forwarder
	log.Debug().Msg("Starting SSH port forwarder...")
	pfw, err := forwarder.NewPortForwarder(&forwarder.PortForwarderOpts{
		DoneChan:      doneChan,
		RemoteConn:    remoteConn,
		LocalPort:     localPort,
		SSHAddr:       ec2fw.LocalAddress(),
		SSHUser:       "ec2-user",
		SSHPrivateKey: pk,
	})
	if err != nil {
		return nil, fmt.Errorf("could not instantiate port forwarder: %w", err)
	}

	return pfw, nil
}
