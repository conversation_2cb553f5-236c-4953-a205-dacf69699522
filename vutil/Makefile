build:
	GOOS=windows  GOARCH=amd64 go build -o bin/windows/vutil.exe         		cmd/main.go
	GOOS=darwin   GOARCH=amd64 go build -o bin/macos-amd64/vutil-x86_64         cmd/main.go
	GOOS=darwin   GOARCH=arm64 go build -o bin/macos-arm64/vutil-arm64         	cmd/main.go
	GOOS=linux    GOARCH=amd64 go build -o bin/linux-amd64/vutil-x86_64         cmd/main.go
	GOOS=linux    GOARCH=arm64 go build -o bin/linux-arm64/vutil-arm64         	cmd/main.go

build-native:
	go build -o bin/macos-arm64/vutil         cmd/main.go 

run:
	go run cmd/main.go

clean:
	rm -fr bin/*