# vutil
`vutil` is a command-line utility that aims to simplify the developer life while working on Viewbid project. It's meant to cut time on tasks that are long, or complex to execute, leaving the developer only focusing on tasks that matter.

# Features
✅: Ready    
🚧: Not ready yet

## ✅ Database Tunnel
Allows open tunnel to dev or qa quickly: `vutil dbtun --env=dev` or `vutil dbtun --env=qa`. By default connects to dev

```
Usage:
  vutil dbtun [flags]

Flags:
      --debug         When set to 'true', will display useful information
  -e, --env string    Environment in which to execute the action (default "dev")
  -h, --help          help for dbtun
  -p, --port string   Local port to listen on that will forward to the remote database (default "5432")
```

## 🚧 Service Restart
To allow developer to quickly restart tasks of a specific service. Developer will win some time, instead of going through the full pipeline deployment process    
  `vutil restart --service=auction-catalogs`

## 🚧 Database record delete
To quickly delete all database records. (Only the records, not the schemas or db structure)    
  `vutil dbdel --db=notifications --env=qa`