package forwarder

import (
	"crypto/rsa"
	"errors"
	"fmt"
	"io"
	"net"

	"github.com/rs/zerolog/log"

	"golang.org/x/crypto/ssh"
)

type PortForwarderOpts struct {
	RemoteConn string
	LocalPort  string

	SSHAddr       string
	SSHPrivateKey *rsa.PrivateKey
	SSHPassword   string
	SSHUser       string

	Done<PERSON>han *chan struct{}
}
type PortForwarder struct {
	localPort     string
	remoteConnStr string
	sshConn       *ssh.Client
	doneChan      *chan struct{}
	localListener net.Listener
}

func NewPortForwarder(opts *PortForwarderOpts) (*PortForwarder, error) {
	sshAuth := []ssh.AuthMethod{}
	if opts.SSHPrivateKey != nil {
		signer, err := ssh.NewSignerFromKey(opts.SSHPrivateKey)
		if err != nil {
			return nil, err
		}
		sshAuth = append(sshAuth, ssh.PublicKeys(signer))
	}

	sshClient, err := ssh.Dial("tcp", opts.SSHAddr, &ssh.ClientConfig{
		User:            opts.SSHUser,
		Auth:            sshAuth,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	})
	if err != nil {
		return nil, err
	}

	return &PortForwarder{
		sshConn:       sshClient,
		remoteConnStr: opts.RemoteConn,
		localPort:     opts.LocalPort,
		doneChan:      opts.DoneChan,
	}, nil
}

func (pfw *PortForwarder) Forward() error {
	// Create listener on specified port
	l, err := net.Listen("tcp", fmt.Sprintf("127.0.0.1:%s", pfw.localPort))
	if err != nil {
		return err
	}
	pfw.localListener = l
	log.Debug().Msgf("Listening on: '%s'", l.Addr().String())
	defer l.Close()

	for {
		// Wait indefinitely for clients to connect or until
		// we receive a manual termination from client (e.g: Ctrl+C signal)
		lc, err := l.Accept()
		if err != nil {
			// Closing unexpectedly is not an error
			if errors.Is(err, net.ErrClosed) {
				return nil
			}

			// Error is valid, we return it
			return err
		}

		rc, err := pfw.sshConn.Dial("tcp", pfw.remoteConnStr)
		if err != nil {
			return fmt.Errorf("could not dial the remote target address '%s', got: %v", pfw.remoteConnStr, err)
		}

		// Read all bytes from local client (request) and forward to remote connection
		go io.Copy(rc, lc)
		go io.Copy(lc, rc)
	}
}

func (pfw *PortForwarder) Wait() {
	<-*pfw.doneChan
	log.Info().Msg("Stopping local listener gracefully...")
	pfw.localListener.Close()
}

func (pfw *PortForwarder) Port() string {
	return pfw.localPort
}
