package forwarder

import (
	"context"
	"crypto/sha256"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/rs/zerolog/log"

	"github.com/aws/aws-sdk-go-v2/aws"
	signerV4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
)

type InstanceConnectForwarderOpts struct {
	ConnectEndpointDNS string
	Instance           *types.Instance
	RemotePort         string
	AWSConfig          *aws.Config
}

type InstanceConnectForwarder struct {
	connectEndpointDNS string
	connectEndpointId  string
	instance           *types.Instance
	remotePort         string
	awsConfig          *aws.Config

	localAddress      string
	listenerReady<PERSON>han chan struct{}
}

func NewInstanceConnectForwarder(opts *InstanceConnectForwarderOpts) *InstanceConnectForwarder {
	eid := strings.Split(opts.ConnectEndpointDNS, ".")[0]

	return &InstanceConnectForwarder{
		connectEndpointId:  eid,
		connectEndpointDNS: opts.ConnectEndpointDNS,
		instance:           opts.Instance,
		remotePort:         opts.RemotePort,
		awsConfig:          opts.AWSConfig,
		listenerReadyChan:  make(chan struct{}, 1),
	}
}

func (icfw *InstanceConnectForwarder) Forward() error {
	var gErr error

	// Create local listener
	l, err := net.Listen("tcp", "127.0.0.1:")
	if err != nil {
		return fmt.Errorf("could not create local listener: %w", err)
	}
	icfw.localAddress = l.Addr().String()
	log.Debug().Msgf("Websocket listening on: %s", icfw.localAddress)
	defer l.Close()

	// Send readiness via channel
	icfw.listenerReadyChan <- struct{}{}

	// Accept connection & forward between local to websocket connection
	for {
		c, err := l.Accept()
		if err != nil {
			gErr = err
			break
		}

		// Establish connection to websocket
		ws, gErr := icfw.connectTunnel()
		if gErr != nil {
			return gErr
		}

		log.Debug().Msg("Received new connection...")

		// Forward reads/writes between local & websocket connection
		go io.Copy(ws.Writer(), c)
		go io.Copy(c, ws.Reader())
	}

	return gErr
}

func (icfw *InstanceConnectForwarder) LocalAddress() string {
	return icfw.localAddress
}

func (icfw *InstanceConnectForwarder) connectTunnel() (*Websocket, error) {
	// Prepare websocket parameters
	params := url.Values{}
	params.Add("instanceConnectEndpointId", icfw.connectEndpointId)
	params.Add("remotePort", icfw.remotePort)
	params.Add("privateIpAddress", *icfw.instance.PrivateIpAddress)
	params.Add("X-Amz-Expires", "60")
	queryString := params.Encode()

	// Generate unsigned URL
	unsignedURL := fmt.Sprintf("wss://%s/openTunnel?%s", icfw.connectEndpointDNS, queryString)

	// Create websocket request
	request, err := http.NewRequestWithContext(context.TODO(), http.MethodGet, unsignedURL, nil)
	if err != nil {
		return nil, err
	}

	// Retrieve the AWS credentials
	creds, err := icfw.awsConfig.Credentials.Retrieve(context.TODO())
	if err != nil {
		return nil, err
	}

	// Hash & sign the request
	hash := fmt.Sprintf("%x", sha256.Sum256([]byte{}))
	signer := signerV4.NewSigner()
	uri, _, err := signer.PresignHTTP(context.TODO(), creds, request, hash, "ec2-instance-connect", "ca-central-1", time.Now())
	if err != nil {
		return nil, err
	}

	// Connect to websocket
	webSocket, err := NewWebSocket(uri)
	if err != nil {
		return nil, err
	}

	return webSocket, nil
}

func (icfw *InstanceConnectForwarder) ReadyChan() chan struct{} {
	return icfw.listenerReadyChan
}
